#!/bin/bash

# AI-Clerk API 测试脚本

BASE_URL="http://localhost:8080"

echo "=== AI-Clerk API 测试 ==="
echo

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/health" | jq .
echo -e "\n"

# 2. 用户注册
echo "2. 用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }')

echo "$REGISTER_RESPONSE" | jq .

# 提取 token
TOKEN=$(echo "$REGISTER_RESPONSE" | jq -r '.data.token')
echo "Token: $TOKEN"
echo -e "\n"

# 3. 用户登录
echo "3. 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

echo "$LOGIN_RESPONSE" | jq .

# 更新 token（使用登录返回的 token）
TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.token')
echo -e "\n"

# 4. 获取用户信息
echo "4. 获取用户信息..."
curl -s -X GET "$BASE_URL/api/v1/profile" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo -e "\n"

# 5. 创建应用
echo "5. 创建应用..."
CREATE_APP_RESPONSE=$(curl -s -X POST "$BASE_URL/api/v1/apps" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "测试应用",
    "description": "这是一个测试应用",
    "icon": "https://example.com/icon.png",
    "config": "{\"key\": \"value\"}"
  }')

echo "$CREATE_APP_RESPONSE" | jq .

# 提取应用 ID
APP_ID=$(echo "$CREATE_APP_RESPONSE" | jq -r '.data.id')
echo "App ID: $APP_ID"
echo -e "\n"

# 6. 获取应用列表
echo "6. 获取应用列表..."
curl -s -X GET "$BASE_URL/api/v1/apps" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo -e "\n"

# 7. 获取单个应用
echo "7. 获取单个应用..."
curl -s -X GET "$BASE_URL/api/v1/apps/$APP_ID" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo -e "\n"

# 8. 更新应用
echo "8. 更新应用..."
curl -s -X PUT "$BASE_URL/api/v1/apps/$APP_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "name": "更新后的应用",
    "description": "这是更新后的描述"
  }' | jq .
echo -e "\n"

# 9. 删除应用
echo "9. 删除应用..."
curl -s -X DELETE "$BASE_URL/api/v1/apps/$APP_ID" \
  -H "Authorization: Bearer $TOKEN" | jq .
echo -e "\n"

echo "=== 测试完成 ==="
