# AI-Clerk

基于 Echo 和 GORM 的简单 Web 项目框架。

## 项目结构

```
ai-clerk/
├── main.go              # 主入口文件
├── config.toml          # 配置文件
├── go.mod              # Go 模块文件
├── handlers/           # 处理器（合并了 handler 和 service）
│   ├── user.go         # 用户相关处理
│   └── app.go          # 应用相关处理
├── models/             # 数据模型
│   ├── user.go         # 用户模型
│   └── app.go          # 应用模型
├── middleware/         # 中间件
│   └── auth.go         # 认证中间件
└── utils/              # 工具类
    ├── db.go           # 数据库工具
    └── redis.go        # Redis 工具
```

## 功能特性

- ✅ 用户注册/登录
- ✅ JWT 认证
- ✅ 用户信息管理
- ✅ 应用 CRUD 操作
- ✅ Redis 缓存支持
- ✅ 数据库连接池
- ✅ 统一错误处理
- ✅ 分页查询

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置数据库

修改 `config.toml` 文件中的数据库配置：

```toml
[database]
host = "localhost"
port = 3306
user = "root"
password = "your_password"
dbname = "ai_clerk"
charset = "utf8mb4"
```

### 3. 创建数据库

```sql
CREATE DATABASE ai_clerk CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 启动服务

```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

## API 接口

### 用户相关

- `POST /api/v1/register` - 用户注册
- `POST /api/v1/login` - 用户登录
- `GET /api/v1/profile` - 获取用户信息（需要认证）
- `PUT /api/v1/profile` - 更新用户信息（需要认证）

### 应用相关

- `GET /api/v1/apps` - 获取应用列表（需要认证）
- `POST /api/v1/apps` - 创建应用（需要认证）
- `GET /api/v1/apps/:id` - 获取单个应用（需要认证）
- `PUT /api/v1/apps/:id` - 更新应用（需要认证）
- `DELETE /api/v1/apps/:id` - 删除应用（需要认证）

### 其他

- `GET /health` - 健康检查

## 请求示例

### 用户注册

```bash
curl -X POST http://localhost:8080/api/v1/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 用户登录

```bash
curl -X POST http://localhost:8080/api/v1/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 创建应用

```bash
curl -X POST http://localhost:8080/api/v1/apps \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "My App",
    "description": "This is my app",
    "icon": "https://example.com/icon.png",
    "config": "{\"key\": \"value\"}"
  }'
```

## 配置说明

### 服务器配置

```toml
[server]
port = ":8080"    # 服务端口
debug = true      # 调试模式
```

### 数据库配置

```toml
[database]
host = "localhost"
port = 3306
user = "root"
password = "password"
dbname = "ai_clerk"
charset = "utf8mb4"
```

### Redis 配置

```toml
[redis]
addr = "localhost:6379"
password = ""
db = 0
```

### JWT 配置

```toml
[jwt]
secret = "your-secret-key-here"
expire_hours = 24
```

## 开发说明

### 项目特点

1. **扁平化结构**：避免过度拆分，保持项目结构简单
2. **合并 Handler 和 Service**：减少层级，提高开发效率
3. **统一错误处理**：使用中间件统一处理错误响应
4. **完整的用户认证**：包含注册、登录、JWT 认证
5. **数据库自动迁移**：启动时自动创建表结构

### 扩展建议

- 添加数据验证中间件
- 实现日志记录
- 添加单元测试
- 实现文件上传功能
- 添加 API 文档（Swagger）

## 许可证

MIT License
