#!/bin/bash

# AI-Clerk 项目设置脚本

set -e

echo "=== AI-Clerk 项目设置 ==="
echo

# 检查 Go 是否安装
if ! command -v go &> /dev/null; then
    echo "错误: Go 未安装，请先安装 Go 1.21 或更高版本"
    exit 1
fi

echo "✓ Go 版本: $(go version)"

# 检查 Docker 是否安装（可选）
if command -v docker &> /dev/null; then
    echo "✓ Docker 版本: $(docker --version)"
else
    echo "⚠ Docker 未安装，无法使用 Docker 部署"
fi

# 检查 MySQL 是否运行（可选）
if command -v mysql &> /dev/null; then
    echo "✓ MySQL 已安装"
else
    echo "⚠ MySQL 未安装，请确保数据库可用"
fi

echo

# 1. 下载依赖
echo "1. 下载 Go 依赖..."
go mod download
go mod tidy
echo "✓ 依赖下载完成"

# 2. 创建必要的目录
echo "2. 创建必要的目录..."
mkdir -p tmp
mkdir -p logs
echo "✓ 目录创建完成"

# 3. 复制配置文件
echo "3. 检查配置文件..."
if [ ! -f "config.local.toml" ]; then
    cp config.toml config.local.toml
    echo "✓ 已创建本地配置文件 config.local.toml"
    echo "  请根据需要修改数据库和 Redis 配置"
else
    echo "✓ 本地配置文件已存在"
fi

# 4. 安装开发工具（可选）
echo "4. 安装开发工具..."
if ! command -v air &> /dev/null; then
    echo "  安装 Air（热重载工具）..."
    go install github.com/cosmtrek/air@latest
    echo "✓ Air 安装完成"
else
    echo "✓ Air 已安装"
fi

# 5. 构建项目
echo "5. 构建项目..."
go build -o ai-clerk .
echo "✓ 项目构建完成"

echo
echo "=== 设置完成 ==="
echo
echo "下一步："
echo "1. 修改 config.local.toml 中的数据库配置"
echo "2. 确保 MySQL 和 Redis 服务正在运行"
echo "3. 运行项目："
echo "   - 开发模式: make dev 或 air"
echo "   - 生产模式: make run 或 ./ai-clerk"
echo "4. 测试 API: ./test_api.sh"
echo
echo "Docker 部署："
echo "   docker-compose up -d"
echo
