version: '3.8'

services:
  # 应用服务
  ai-clerk:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=password
      - DB_NAME=ai_clerk
      - REDIS_ADDR=redis:6379
    depends_on:
      - mysql
      - redis
    volumes:
      - ./config.toml:/root/config.toml
    networks:
      - ai-clerk-network

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: ai_clerk
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai-clerk-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-clerk-network

  # phpMyAdmin（可选，用于数据库管理）
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - ai-clerk-network

volumes:
  mysql_data:
  redis_data:

networks:
  ai-clerk-network:
    driver: bridge
