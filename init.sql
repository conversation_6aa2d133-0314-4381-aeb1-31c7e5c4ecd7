-- 初始化数据库脚本

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ai_clerk CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE ai_clerk;

-- 创建用户表（GORM 会自动创建，这里只是示例）
-- CREATE TABLE IF NOT EXISTS users (
--     id bigint unsigned NOT NULL AUTO_INCREMENT,
--     created_at datetime(3) DEFAULT NULL,
--     updated_at datetime(3) DEFAULT NULL,
--     deleted_at datetime(3) DEFAULT NULL,
--     username varchar(191) NOT NULL,
--     email varchar(191) NOT NULL,
--     password varchar(191) NOT NULL,
--     avatar varchar(191) DEFAULT NULL,
--     status int DEFAULT 1,
--     PRIMARY KEY (id),
--     UNIQUE KEY idx_users_username (username),
--     UNIQUE KEY idx_users_email (email),
--     KEY idx_users_deleted_at (deleted_at)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建应用表（GORM 会自动创建，这里只是示例）
-- CREATE TABLE IF NOT EXISTS apps (
--     id bigint unsigned NOT NULL AUTO_INCREMENT,
--     created_at datetime(3) DEFAULT NULL,
--     updated_at datetime(3) DEFAULT NULL,
--     deleted_at datetime(3) DEFAULT NULL,
--     name varchar(191) NOT NULL,
--     description varchar(191) DEFAULT NULL,
--     icon varchar(191) DEFAULT NULL,
--     status int DEFAULT 1,
--     config text,
--     user_id bigint unsigned NOT NULL,
--     PRIMARY KEY (id),
--     KEY idx_apps_deleted_at (deleted_at),
--     KEY idx_apps_user_id (user_id)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据（可选）
-- INSERT INTO users (username, email, password, status, created_at, updated_at) VALUES
-- ('admin', '<EMAIL>', '$2a$10$example_hashed_password', 1, NOW(), NOW());

-- 设置时区
SET time_zone = '+08:00';
