package models

import (
	"time"

	"gorm.io/gorm"
)

type App struct {
	ID        uint           `json:"id" gorm:"primarykey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
	
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description"`
	Icon        string `json:"icon"`
	Status      int    `json:"status" gorm:"default:1"` // 1: 正常, 0: 禁用
	Config      string `json:"config" gorm:"type:text"` // JSON 配置
	
	// 外键
	UserID uint `json:"user_id" gorm:"not null;index"`
	User   User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

type AppCreateRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=100"`
	Description string `json:"description" validate:"max=500"`
	Icon        string `json:"icon"`
	Config      string `json:"config"`
}

type AppUpdateRequest struct {
	Name        string `json:"name" validate:"min=1,max=100"`
	Description string `json:"description" validate:"max=500"`
	Icon        string `json:"icon"`
	Status      *int   `json:"status"`
	Config      string `json:"config"`
}

type AppResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Icon        string    `json:"icon"`
	Status      int       `json:"status"`
	Config      string    `json:"config"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
