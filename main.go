package main

import (
	"ai-clerk/handlers"
	"ai-clerk/middleware"
	"ai-clerk/models"
	"ai-clerk/utils"
	"log"

	"github.com/labstack/echo/v4"
	echomiddleware "github.com/labstack/echo/v4/middleware"
	"github.com/spf13/viper"
)

func main() {
	// 初始化配置
	initConfig()

	// 初始化数据库
	utils.InitDB()
	utils.InitRedis()

	// 自动迁移数据库表
	if err := utils.DB.AutoMigrate(&models.User{}, &models.App{}); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 创建 Echo 实例
	e := echo.New()

	// 中间件
	e.Use(echomiddleware.Logger())
	e.Use(echomiddleware.Recover())
	e.Use(echomiddleware.CORS())

	// 路由
	setupRoutes(e)

	// 启动服务器
	port := viper.GetString("server.port")
	log.Printf("Server starting on port %s", port)
	e.Logger.Fatal(e.Start(port))
}

func initConfig() {
	viper.SetConfigName("config")
	viper.SetConfigType("toml")
	viper.AddConfigPath(".")
	
	if err := viper.ReadInConfig(); err != nil {
		log.Fatal("Error reading config file:", err)
	}
}

func setupRoutes(e *echo.Echo) {
	// 健康检查
	e.GET("/health", func(c echo.Context) error {
		return c.JSON(200, map[string]string{"status": "ok"})
	})

	// API 路由组
	api := e.Group("/api/v1")

	// 用户相关路由
	userHandler := &handlers.UserHandler{}
	api.POST("/register", userHandler.Register)
	api.POST("/login", userHandler.Login)
	
	// 需要认证的路由
	auth := api.Group("")
	auth.Use(middleware.JWTAuth())
	auth.GET("/profile", userHandler.GetProfile)
	auth.PUT("/profile", userHandler.UpdateProfile)

	// 应用相关路由
	appHandler := &handlers.AppHandler{}
	auth.GET("/apps", appHandler.GetApps)
	auth.POST("/apps", appHandler.CreateApp)
	auth.GET("/apps/:id", appHandler.GetApp)
	auth.PUT("/apps/:id", appHandler.UpdateApp)
	auth.DELETE("/apps/:id", appHandler.DeleteApp)
}
