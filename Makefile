.PHONY: build run clean test deps

# 应用名称
APP_NAME = ai-clerk

# 构建目录
BUILD_DIR = build

# Go 相关命令
GO = go
GOFMT = gofmt
GOVET = go vet
GOTEST = go test

# 默认目标
all: deps fmt vet test build

# 安装依赖
deps:
	$(GO) mod download
	$(GO) mod tidy

# 格式化代码
fmt:
	$(GOFMT) -s -w .

# 代码检查
vet:
	$(GOVET) ./...

# 运行测试
test:
	$(GOTEST) -v ./...

# 构建应用
build:
	mkdir -p $(BUILD_DIR)
	$(GO) build -o $(BUILD_DIR)/$(APP_NAME) .

# 运行应用
run:
	$(GO) run main.go

# 开发模式运行（带热重载，需要安装 air）
dev:
	air

# 清理构建文件
clean:
	rm -rf $(BUILD_DIR)
	$(GO) clean

# 安装开发工具
install-tools:
	go install github.com/cosmtrek/air@latest

# 数据库迁移（需要先启动应用）
migrate:
	curl -X GET http://localhost:8080/health

# 检查代码质量
lint:
	golangci-lint run

# 生成 API 文档（如果使用 swag）
docs:
	swag init

# Docker 相关
docker-build:
	docker build -t $(APP_NAME) .

docker-run:
	docker run -p 8080:8080 $(APP_NAME)

# 帮助信息
help:
	@echo "Available commands:"
	@echo "  deps         - Download and tidy dependencies"
	@echo "  fmt          - Format Go code"
	@echo "  vet          - Run go vet"
	@echo "  test         - Run tests"
	@echo "  build        - Build the application"
	@echo "  run          - Run the application"
	@echo "  dev          - Run in development mode with hot reload"
	@echo "  clean        - Clean build files"
	@echo "  install-tools- Install development tools"
	@echo "  migrate      - Test database migration"
	@echo "  lint         - Run linter"
	@echo "  docs         - Generate API documentation"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  help         - Show this help message"
