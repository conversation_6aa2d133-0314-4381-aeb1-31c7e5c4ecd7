package handlers

import (
	"ai-clerk/middleware"
	"ai-clerk/models"
	"ai-clerk/utils"
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type UserHandler struct{}

// 用户注册
func (h *UserHandler) Register(c echo.Context) error {
	var req models.UserRegisterRequest
	if err := c.Bind(&req); err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid request data")
	}

	// 检查用户名是否已存在
	var existingUser models.User
	if err := utils.DB.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		return middleware.ErrorResponse(c, http.StatusConflict, "Username or email already exists")
	}

	// 加密密码
	hashedPassword, err := middleware.HashPassword(req.Password)
	if err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to hash password")
	}

	// 创建用户
	user := models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: hashedPassword,
		Status:   1,
	}

	if err := utils.DB.Create(&user).Error; err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to create user")
	}

	// 生成 token
	token, err := middleware.GenerateToken(user.ID, user.Email)
	if err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate token")
	}

	response := models.LoginResponse{
		Token: token,
		User: models.UserResponse{
			ID:       user.ID,
			Username: user.Username,
			Email:    user.Email,
			Avatar:   user.Avatar,
			Status:   user.Status,
		},
	}

	return middleware.SuccessResponse(c, response)
}

// 用户登录
func (h *UserHandler) Login(c echo.Context) error {
	var req models.UserLoginRequest
	if err := c.Bind(&req); err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid request data")
	}

	// 查找用户
	var user models.User
	if err := utils.DB.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return middleware.ErrorResponse(c, http.StatusUnauthorized, "Invalid email or password")
		}
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Database error")
	}

	// 验证密码
	if !middleware.CheckPassword(req.Password, user.Password) {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "Invalid email or password")
	}

	// 检查用户状态
	if user.Status != 1 {
		return middleware.ErrorResponse(c, http.StatusForbidden, "User account is disabled")
	}

	// 生成 token
	token, err := middleware.GenerateToken(user.ID, user.Email)
	if err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to generate token")
	}

	response := models.LoginResponse{
		Token: token,
		User: models.UserResponse{
			ID:       user.ID,
			Username: user.Username,
			Email:    user.Email,
			Avatar:   user.Avatar,
			Status:   user.Status,
		},
	}

	return middleware.SuccessResponse(c, response)
}

// 获取用户信息
func (h *UserHandler) GetProfile(c echo.Context) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated")
	}

	var user models.User
	if err := utils.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return middleware.ErrorResponse(c, http.StatusNotFound, "User not found")
		}
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Database error")
	}

	response := models.UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Avatar:   user.Avatar,
		Status:   user.Status,
	}

	return middleware.SuccessResponse(c, response)
}

// 更新用户信息
func (h *UserHandler) UpdateProfile(c echo.Context) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated")
	}

	var req models.UserUpdateRequest
	if err := c.Bind(&req); err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid request data")
	}

	var user models.User
	if err := utils.DB.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return middleware.ErrorResponse(c, http.StatusNotFound, "User not found")
		}
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Database error")
	}

	// 更新字段
	if req.Username != "" {
		// 检查用户名是否已被其他用户使用
		var existingUser models.User
		if err := utils.DB.Where("username = ? AND id != ?", req.Username, userID).First(&existingUser).Error; err == nil {
			return middleware.ErrorResponse(c, http.StatusConflict, "Username already exists")
		}
		user.Username = req.Username
	}

	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}

	if err := utils.DB.Save(&user).Error; err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to update user")
	}

	response := models.UserResponse{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Avatar:   user.Avatar,
		Status:   user.Status,
	}

	return middleware.SuccessResponse(c, response)
}
