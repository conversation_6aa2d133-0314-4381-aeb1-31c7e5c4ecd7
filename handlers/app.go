package handlers

import (
	"ai-clerk/middleware"
	"ai-clerk/models"
	"ai-clerk/utils"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type AppHandler struct{}

// 获取应用列表
func (h *AppHandler) GetApps(c echo.Context) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated")
	}

	// 获取分页参数
	var pagination middleware.PaginationParams
	if err := c.Bind(&pagination); err != nil {
		pagination.Page = 1
		pagination.PageSize = 10
	}

	var apps []models.App
	var total int64

	// 查询总数
	utils.DB.Model(&models.App{}).Where("user_id = ?", userID).Count(&total)

	// 查询应用列表
	if err := utils.DB.Where("user_id = ?", userID).
		Offset(pagination.GetOffset()).
		Limit(pagination.GetLimit()).
		Order("created_at DESC").
		Find(&apps).Error; err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Database error")
	}

	// 转换为响应格式
	var responses []models.AppResponse
	for _, app := range apps {
		responses = append(responses, models.AppResponse{
			ID:          app.ID,
			Name:        app.Name,
			Description: app.Description,
			Icon:        app.Icon,
			Status:      app.Status,
			Config:      app.Config,
			CreatedAt:   app.CreatedAt,
			UpdatedAt:   app.UpdatedAt,
		})
	}

	data := map[string]interface{}{
		"apps":      responses,
		"total":     total,
		"page":      pagination.Page,
		"page_size": pagination.GetLimit(),
	}

	return middleware.SuccessResponse(c, data)
}

// 创建应用
func (h *AppHandler) CreateApp(c echo.Context) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated")
	}

	var req models.AppCreateRequest
	if err := c.Bind(&req); err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid request data")
	}

	// 创建应用
	app := models.App{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		Config:      req.Config,
		Status:      1,
		UserID:      userID,
	}

	if err := utils.DB.Create(&app).Error; err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to create app")
	}

	response := models.AppResponse{
		ID:          app.ID,
		Name:        app.Name,
		Description: app.Description,
		Icon:        app.Icon,
		Status:      app.Status,
		Config:      app.Config,
		CreatedAt:   app.CreatedAt,
		UpdatedAt:   app.UpdatedAt,
	}

	return middleware.SuccessResponse(c, response)
}

// 获取单个应用
func (h *AppHandler) GetApp(c echo.Context) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated")
	}

	appID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid app ID")
	}

	var app models.App
	if err := utils.DB.Where("id = ? AND user_id = ?", appID, userID).First(&app).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return middleware.ErrorResponse(c, http.StatusNotFound, "App not found")
		}
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Database error")
	}

	response := models.AppResponse{
		ID:          app.ID,
		Name:        app.Name,
		Description: app.Description,
		Icon:        app.Icon,
		Status:      app.Status,
		Config:      app.Config,
		CreatedAt:   app.CreatedAt,
		UpdatedAt:   app.UpdatedAt,
	}

	return middleware.SuccessResponse(c, response)
}

// 更新应用
func (h *AppHandler) UpdateApp(c echo.Context) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated")
	}

	appID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid app ID")
	}

	var req models.AppUpdateRequest
	if err := c.Bind(&req); err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid request data")
	}

	var app models.App
	if err := utils.DB.Where("id = ? AND user_id = ?", appID, userID).First(&app).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return middleware.ErrorResponse(c, http.StatusNotFound, "App not found")
		}
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Database error")
	}

	// 更新字段
	if req.Name != "" {
		app.Name = req.Name
	}
	if req.Description != "" {
		app.Description = req.Description
	}
	if req.Icon != "" {
		app.Icon = req.Icon
	}
	if req.Status != nil {
		app.Status = *req.Status
	}
	if req.Config != "" {
		app.Config = req.Config
	}

	if err := utils.DB.Save(&app).Error; err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to update app")
	}

	response := models.AppResponse{
		ID:          app.ID,
		Name:        app.Name,
		Description: app.Description,
		Icon:        app.Icon,
		Status:      app.Status,
		Config:      app.Config,
		CreatedAt:   app.CreatedAt,
		UpdatedAt:   app.UpdatedAt,
	}

	return middleware.SuccessResponse(c, response)
}

// 删除应用
func (h *AppHandler) DeleteApp(c echo.Context) error {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		return middleware.ErrorResponse(c, http.StatusUnauthorized, "User not authenticated")
	}

	appID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		return middleware.ErrorResponse(c, http.StatusBadRequest, "Invalid app ID")
	}

	var app models.App
	if err := utils.DB.Where("id = ? AND user_id = ?", appID, userID).First(&app).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return middleware.ErrorResponse(c, http.StatusNotFound, "App not found")
		}
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Database error")
	}

	if err := utils.DB.Delete(&app).Error; err != nil {
		return middleware.ErrorResponse(c, http.StatusInternalServerError, "Failed to delete app")
	}

	return middleware.SuccessResponse(c, map[string]string{"message": "App deleted successfully"})
}
